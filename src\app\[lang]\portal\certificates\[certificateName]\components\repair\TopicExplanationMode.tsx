"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { motion, AnimatePresence } from "framer-motion";
import Confetti from "react-canvas-confetti/dist/presets/realistic";
import {
  createTopicExplanationAttempt,
  addTopicExplanationQuestions,
  submitInitialTopicAnswer,
  addInitialAnalysisToTopicExplanation,
  submitFollowUpAnswer,
  addFollowUpAnalysisToTopicExplanation,
  getTopicExplanationQuestions,
  markQuestionAsMastered,
  TopicExplanationQuestion
} from "@/Firebase/firestore/services/RepairCenterExamService";
import { serverTimestamp, Timestamp } from 'firebase/firestore';
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  ArrowLeft,
  ArrowRight,
  Clock,
  MessageSquare,
  Brain,
  CheckCircle,
  XCircle,
  Loader2,
  Target,
  BookOpen,
  AlertTriangle,
  Trophy
} from "lucide-react";

interface TopicExplanationModeProps {
  certificate: Certificate;
  repairCenterQuestions: Array<{
    questionId: string;
    question: {
      question?: string;
      choiceA?: string;
      choiceB?: string;
      choiceC?: string;
      choiceD?: string;
      correctAnswer?: string;
      category?: string;
    };
    priority: string;
    successRate: number;
    topics: string[];
  }>;
  onBack: () => void;
}

export default function TopicExplanationMode({
  certificate,
  repairCenterQuestions,
  onBack
}: TopicExplanationModeProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<'A' | 'B' | 'C' | 'D' | null>(null);
  const [userExplanation, setUserExplanation] = useState("");
  const [followUpAnswer, setFollowUpAnswer] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [attemptId, setAttemptId] = useState<string | null>(null);
  const [topicQuestions, setTopicQuestions] = useState<TopicExplanationQuestion[]>([]);
  const [currentPhase, setCurrentPhase] = useState<'answer' | 'follow_up' | 'choice' | 'completed'>('answer');
  const [currentAnalysis, setCurrentAnalysis] = useState<{
    understandingScore: number;
    isAnswerCorrect: boolean;
    mainTopicsAssessed: string[];
    feedback: string;
    knowledgeGaps: string[];
    shouldProceedToNext: boolean;
    allowSkipFollowUp?: boolean;
    correctAnswerExplanation?: {
      whyCorrect: string;
      keyPrinciples: string[];
      relatedTopics: string[];
      practicalApplication: string;
      whyOthersWrong: {
        A?: string;
        B?: string;
        C?: string;
        D?: string;
      };
    };
    followUpQuestion?: {
      type: 'why_correct' | 'why_incorrect' | 'scenario_application';
      question: string;
      expectedPoints: string[];
      minimizedTopicHint: string;
    };
    followUpAnalysis?: {
      followUpScore: number;
      overallUnderstanding: number;
      feedback: string;
      conceptsMastered: string[];
      areasForImprovement: string[];
      mainTopicsAssessed: string[];
      readyForNextQuestion: boolean;
      studyRecommendations: string[];
    };
  } | null>(null);
  const [startTime] = useState(Date.now());
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [questionAttempts, setQuestionAttempts] = useState<Map<number, number>>(new Map());
  const [showMasteryAnimation, setShowMasteryAnimation] = useState(false);
  const [masteredQuestionData, setMasteredQuestionData] = useState<{
    score: number;
    questionNumber: number;
    totalQuestions: number;
  } | null>(null);
  const [isQuestionBeingWiped, setIsQuestionBeingWiped] = useState(false);
  const confettiRef = useRef<((options?: any) => void) | null>(null);

  const { user } = useAuth();
  const { toast } = useToast();

  // Function to mark question as completed and update local state
  const markQuestionCompleted = (questionIndex: number) => {
    setTopicQuestions(prev =>
      prev.map((q, index) =>
        index === questionIndex
          ? { ...q, status: 'completed' as const }
          : q
      )
    );
  };

  // Manual mastery function - when user overrides AI assessment
  const handleManualMastery = async () => {
    if (!attemptId || !topicQuestions[currentQuestionIndex]?.id || !currentAnalysis || !user?.uid || !certificate.id) return;

    try {
      const currentAttempts = questionAttempts.get(currentQuestionIndex) || 0;

      // Mark as mastered in database
      await markQuestionAsMastered(
        user.uid,
        certificate.id,
        repairCenterQuestions[currentQuestionIndex].questionId,
        {
          masteredAt: serverTimestamp() as Timestamp,
          finalScore: currentAnalysis.understandingScore,
          attempts: currentAttempts + 1,
          masteryMethod: 'topic_explanation'
        }
      );

      // Trigger mastery celebration
      triggerMasteryCelebration(currentAnalysis.understandingScore);

      setCurrentPhase('completed');
      toast({
        title: "🎉 Question Marked as Mastered!",
        description: `You've confirmed your understanding. This question will be removed from your Repair Center.`,
      });
    } catch (error) {
      console.error('Error marking question as mastered:', error);
      toast({
        title: "Error",
        description: "Failed to mark question as mastered. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Mastery celebration function
  const triggerMasteryCelebration = (score: number) => {
    // First, trigger the wipe animation for the current question
    setIsQuestionBeingWiped(true);

    // After a short delay, show the mastery celebration
    setTimeout(() => {
      setMasteredQuestionData({
        score,
        questionNumber: currentQuestionIndex + 1,
        totalQuestions: topicQuestions.length
      });
      setShowMasteryAnimation(true);

      // Trigger confetti
      if (confettiRef.current) {
        confettiRef.current({
          particleCount: 100,
          spread: 70,
          origin: { y: 0.6 }
        });

        // Additional confetti bursts
        setTimeout(() => {
          if (confettiRef.current) {
            confettiRef.current({
              particleCount: 50,
              spread: 60,
              origin: { y: 0.7, x: 0.3 }
            });
          }
        }, 200);

        setTimeout(() => {
          if (confettiRef.current) {
            confettiRef.current({
              particleCount: 50,
              spread: 60,
              origin: { y: 0.7, x: 0.7 }
            });
          }
        }, 400);
      }
    }, 800);

    // Auto-hide animation after 4 seconds
    setTimeout(() => {
      setShowMasteryAnimation(false);
      setMasteredQuestionData(null);
      setIsQuestionBeingWiped(false);
    }, 5000);
  };

  const initializeTopicQuestions = useCallback(async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      // Create topic explanation attempt with repair center questions
      const topics = [...new Set(repairCenterQuestions.flatMap(q => q.topics))];
      const attemptId = await createTopicExplanationAttempt(
        user.uid,
        certificate.id,
        topics
      );
      setAttemptId(attemptId);

      // Convert repair center questions to topic explanation questions
      const topicQuestionData = repairCenterQuestions.map((repairQ) => ({
        originalQuestionId: repairQ.questionId,
        originalQuestion: {
          question: repairQ.question.question || '',
          choiceA: repairQ.question.choiceA || '',
          choiceB: repairQ.question.choiceB || '',
          choiceC: repairQ.question.choiceC || '',
          choiceD: repairQ.question.choiceD || '',
          correctAnswer: (repairQ.question.correctAnswer || 'A') as 'A' | 'B' | 'C' | 'D',
          category: repairQ.question.category
        },
        status: 'initial' as const
      }));

      await addTopicExplanationQuestions(attemptId, topicQuestionData);

      // Fetch the created questions with IDs
      const createdQuestions = await getTopicExplanationQuestions(attemptId);
      setTopicQuestions(createdQuestions);

    } catch (error) {
      console.error('Error initializing topic questions:', error);
      toast({
        title: "Error",
        description: "Failed to initialize topic explanation mode.",
        variant: "destructive",
      });
    }
  }, [user?.uid, certificate.id, repairCenterQuestions, toast]);

  useEffect(() => {
    initializeTopicQuestions();
  }, [initializeTopicQuestions]);

  const handleSubmitInitialAnswer = async () => {
    if (!attemptId || !selectedAnswer || !userExplanation.trim() || !topicQuestions[currentQuestionIndex]?.id) return;

    try {
      setIsSubmitting(true);
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
      const currentQuestion = topicQuestions[currentQuestionIndex];

      // Submit the initial answer and explanation
      await submitInitialTopicAnswer(
        attemptId,
        currentQuestion.id!,
        selectedAnswer,
        userExplanation,
        timeSpent
      );

      // Analyze with AI
      setIsAnalyzing(true);
      const response = await fetch('/api/AnalyzeDeepUnderstanding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          question: currentQuestion.originalQuestion.question,
          choiceA: currentQuestion.originalQuestion.choiceA,
          choiceB: currentQuestion.originalQuestion.choiceB,
          choiceC: currentQuestion.originalQuestion.choiceC,
          choiceD: currentQuestion.originalQuestion.choiceD,
          correctAnswer: currentQuestion.originalQuestion.correctAnswer,
          userAnswer: selectedAnswer,
          userExplanation,
          certificateName: certificate.name,
          isFollowUp: false
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Store AI analysis
          await addInitialAnalysisToTopicExplanation(
            attemptId,
            currentQuestion.id!,
            {
              understandingScore: result.data.understandingScore,
              isAnswerCorrect: result.data.isAnswerCorrect,
              mainTopicsAssessed: result.data.mainTopicsAssessed,
              feedback: result.data.feedback,
              knowledgeGaps: result.data.knowledgeGaps,
              shouldProceedToNext: result.data.shouldProceedToNext
            },
            result.data.followUpQuestion
          );

          setCurrentAnalysis(result.data);

          // Track attempts
          const currentAttempts = questionAttempts.get(currentQuestionIndex) || 0;
          setQuestionAttempts(prev => new Map(prev.set(currentQuestionIndex, currentAttempts + 1)));

          // Handle correct answers
          if (result.data.isAnswerCorrect) {
            // Mark as mastered if understanding is good enough
            if (result.data.understandingScore >= 7 && user?.uid && certificate.id) {
              await markQuestionAsMastered(
                user.uid,
                certificate.id,
                repairCenterQuestions[currentQuestionIndex].questionId,
                {
                  masteredAt: serverTimestamp() as Timestamp,
                  finalScore: result.data.understandingScore,
                  attempts: currentAttempts + 1,
                  masteryMethod: 'topic_explanation'
                }
              );

              // Mark question as completed and trigger mastery celebration
              markQuestionCompleted(currentQuestionIndex);
              triggerMasteryCelebration(result.data.understandingScore);

              setCurrentPhase('completed');
              toast({
                title: "🎉 Question Mastered!",
                description: `Excellent understanding (${result.data.understandingScore}/10)! This question will be removed from your Repair Center.`,
              });
            } else if (result.data.allowSkipFollowUp && result.data.followUpQuestion) {
              // Answer correct but explanation could be better - give choice
              markQuestionCompleted(currentQuestionIndex);
              setCurrentPhase('choice');
              toast({
                title: "Correct Answer!",
                description: `Score: ${result.data.understandingScore}/10 - Choose to continue or improve understanding.`,
              });
            } else {
              // Correct answer but not mastery level
              markQuestionCompleted(currentQuestionIndex);
              setCurrentPhase('completed');
              toast({
                title: "Correct! Good progress",
                description: `Score: ${result.data.understandingScore}/10 - Keep practicing to master this concept.`,
              });
            }
          } else {
            // Handle incorrect answers - show tutoring
            markQuestionCompleted(currentQuestionIndex);
            setCurrentPhase('completed');
            toast({
              title: "Learning Opportunity",
              description: `Review the detailed explanation below. This question will stay in your Repair Center for more practice.`,
              variant: "default",
            });
          }
        }
      }

    } catch (error) {
      console.error('Error submitting initial answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit answer.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setIsAnalyzing(false);
    }
  };

  const handleSubmitFollowUp = async () => {
    if (!attemptId || !followUpAnswer.trim() || !topicQuestions[currentQuestionIndex]?.id) return;

    try {
      setIsSubmitting(true);
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
      const currentQuestion = topicQuestions[currentQuestionIndex];

      // Submit the follow-up answer
      await submitFollowUpAnswer(
        attemptId,
        currentQuestion.id!,
        followUpAnswer,
        timeSpent
      );

      // Analyze follow-up with AI
      setIsAnalyzing(true);
      const response = await fetch('/api/AnalyzeDeepUnderstanding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userExplanation: followUpAnswer,
          isFollowUp: true,
          followUpType: currentAnalysis?.followUpQuestion?.type,
          originalQuestion: currentQuestion.originalQuestion,
          certificateName: certificate.name
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Store follow-up analysis
          await addFollowUpAnalysisToTopicExplanation(
            attemptId,
            currentQuestion.id!,
            {
              followUpScore: result.data.followUpScore,
              overallUnderstanding: result.data.overallUnderstanding,
              feedback: result.data.feedback,
              conceptsMastered: result.data.conceptsMastered,
              areasForImprovement: result.data.areasForImprovement,
              mainTopicsAssessed: result.data.mainTopicsAssessed,
              readyForNextQuestion: result.data.readyForNextQuestion,
              studyRecommendations: result.data.studyRecommendations
            }
          );

          setCurrentAnalysis(prev => prev ? { ...prev, followUpAnalysis: result.data } : null);
          markQuestionCompleted(currentQuestionIndex);
          setCurrentPhase('completed');

          toast({
            title: result.data.readyForNextQuestion ? "Great understanding!" : "Keep studying",
            description: `Overall Score: ${result.data.overallUnderstanding}/10`,
            variant: result.data.readyForNextQuestion ? "default" : "destructive",
          });
        }
      }

    } catch (error) {
      console.error('Error submitting follow-up answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit follow-up answer.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setIsAnalyzing(false);
    }
  };

  const handleChoiceToFollowUp = () => {
    setCurrentPhase('follow_up');
    toast({
      title: "Great choice!",
      description: "Let's deepen your understanding with a follow-up question.",
    });
  };

  const handleChoiceToSkip = async () => {
    if (!attemptId || !topicQuestions[currentQuestionIndex]?.id || !currentAnalysis) return;

    try {
      const currentQuestion = topicQuestions[currentQuestionIndex];

      // Mark as completed without follow-up
      await addInitialAnalysisToTopicExplanation(
        attemptId,
        currentQuestion.id!,
        {
          understandingScore: currentAnalysis.understandingScore,
          isAnswerCorrect: currentAnalysis.isAnswerCorrect,
          mainTopicsAssessed: currentAnalysis.mainTopicsAssessed,
          feedback: currentAnalysis.feedback + " (User chose to skip follow-up)",
          knowledgeGaps: currentAnalysis.knowledgeGaps,
          shouldProceedToNext: true
        }
      );

      markQuestionCompleted(currentQuestionIndex);
      setCurrentPhase('completed');
      toast({
        title: "Moving to next question",
        description: "You can always come back to review this later.",
      });
    } catch (error) {
      console.error('Error skipping follow-up:', error);
      toast({
        title: "Error",
        description: "Failed to skip follow-up. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < topicQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
      setUserExplanation("");
      setFollowUpAnswer("");
      setCurrentPhase('answer');
      setCurrentAnalysis(null);
      setQuestionStartTime(Date.now());
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setSelectedAnswer(null);
      setUserExplanation("");
      setFollowUpAnswer("");
      setCurrentPhase('answer');
      setCurrentAnalysis(null);
      setQuestionStartTime(Date.now());
    }
  };

  const currentQuestion = topicQuestions[currentQuestionIndex];
  const completedQuestions = topicQuestions.filter(q => q.status === 'completed').length;

  if (!currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading topic questions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
      <motion.div
        className="max-w-4xl mx-auto px-4 py-6"
        animate={isQuestionBeingWiped ? {
          x: [0, -20, 20, -10, 10, 0],
          opacity: [1, 0.8, 0.6, 0.8, 1]
        } : {}}
        transition={{ duration: 0.8 }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Repair Center
          </Button>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-1">
              <Brain className="h-3 w-3" />
              Question {currentQuestionIndex + 1} of {topicQuestions.length}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {Math.floor((Date.now() - startTime) / 60000)}m
            </Badge>
            <Badge variant="outline">
              Phase: {currentPhase === 'answer' ? 'Answer' : currentPhase === 'follow_up' ? 'Follow-up' : currentPhase === 'choice' ? 'Choose' : 'Complete'}
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Progress</span>
            <div className="flex items-center gap-2">
              <span>{completedQuestions} / {topicQuestions.length} completed</span>
              {isQuestionBeingWiped && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center gap-1 text-green-600 dark:text-green-400"
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    ✨
                  </motion.div>
                  <span className="text-xs font-medium">Mastering...</span>
                </motion.div>
              )}
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <motion.div
              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(completedQuestions / topicQuestions.length) * 100}%` }}
              animate={isQuestionBeingWiped ? {
                backgroundColor: ["#9333ea", "#10b981", "#9333ea"]
              } : {}}
              transition={{ duration: 0.8 }}
            />
          </div>
        </div>

        {/* Main GDPR Topics */}
        {currentAnalysis?.mainTopicsAssessed && (
          <div className="mb-6">
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200">
              <CardContent className="p-4">
                <h3 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">
                  Main GDPR Topics Being Assessed:
                </h3>
                <div className="flex flex-wrap gap-2">
                  {currentAnalysis.mainTopicsAssessed.map((topic: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs bg-blue-100 text-blue-800 border-blue-300">
                      {topic}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Current Question */}
        {currentPhase === 'answer' && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-purple-600" />
                Answer the Question You Got Wrong
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 dark:text-gray-300 mb-6 text-lg">
                {currentQuestion.originalQuestion.question}
              </p>

              <RadioGroup value={selectedAnswer || ""} onValueChange={(value) => setSelectedAnswer(value as 'A' | 'B' | 'C' | 'D')}>
                <div className="space-y-3 mb-6">
                  {(['A', 'B', 'C', 'D'] as const).map((choice) => (
                    <div key={choice} className="flex items-center space-x-2 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800">
                      <RadioGroupItem value={choice} id={choice} />
                      <Label htmlFor={choice} className="flex-1 cursor-pointer">
                        <span className="font-medium mr-2">{choice})</span>
                        {currentQuestion.originalQuestion[`choice${choice}` as keyof typeof currentQuestion.originalQuestion] as string}
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>

              <div className="mb-4">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                  Explain why you chose this answer:
                </Label>
                <Textarea
                  value={userExplanation}
                  onChange={(e) => setUserExplanation(e.target.value)}
                  placeholder="Explain your reasoning for choosing this answer. Be specific about the concepts and principles involved."
                  className="min-h-[120px]"
                />
              </div>

              <Button
                onClick={handleSubmitInitialAnswer}
                disabled={!selectedAnswer || !userExplanation.trim() || isSubmitting || isAnalyzing}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : isAnalyzing ? (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submit Answer & Explanation
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Follow-up Question */}
        {currentPhase === 'follow_up' && currentAnalysis?.followUpQuestion && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-orange-600" />
                Follow-up Question - Test Your Understanding
              </CardTitle>
              {currentAnalysis.followUpQuestion.minimizedTopicHint && (
                <Badge variant="outline" className="text-xs">
                  Hint: {currentAnalysis.followUpQuestion.minimizedTopicHint}
                </Badge>
              )}
            </CardHeader>
            <CardContent>
              {/* Original Question Reference */}
              <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Original Question:
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {currentQuestion.originalQuestion.question}
                </p>
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  <span className="font-medium">Your Answer:</span> {selectedAnswer} |
                  <span className="font-medium"> Correct Answer:</span> {currentQuestion.originalQuestion.correctAnswer}
                  <span className="text-green-600 ml-1">✓</span>
                </div>
              </div>

              <p className="text-gray-700 dark:text-gray-300 mb-4 text-lg">
                {currentAnalysis.followUpQuestion.question}
              </p>

              <div className="mb-4">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                  Your answer:
                </Label>
                <Textarea
                  value={followUpAnswer}
                  onChange={(e) => setFollowUpAnswer(e.target.value)}
                  placeholder="Provide a detailed explanation demonstrating your understanding..."
                  className="min-h-[150px]"
                />
              </div>

              <Button
                onClick={handleSubmitFollowUp}
                disabled={!followUpAnswer.trim() || isSubmitting || isAnalyzing}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : isAnalyzing ? (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submit Follow-up Answer
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Choice Phase - Skip or Continue with Follow-up */}
        {currentPhase === 'choice' && currentAnalysis && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Correct Answer! Let&apos;s understand it better
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Question Review for Choice Phase */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  {currentQuestion.originalQuestion.question}
                </h3>

                {/* Answer Choices with Correct Answer Highlighted */}
                <div className="space-y-3 mb-4">
                  {(['A', 'B', 'C', 'D'] as const).map((choice) => {
                    const isCorrect = choice === currentQuestion.originalQuestion.correctAnswer;
                    const isUserAnswer = choice === selectedAnswer;
                    const choiceText = currentQuestion.originalQuestion[`choice${choice}` as keyof typeof currentQuestion.originalQuestion] as string;

                    return (
                      <div
                        key={choice}
                        className={`p-3 rounded-lg border-2 ${
                          isCorrect && isUserAnswer
                            ? 'bg-green-50 border-green-300 dark:bg-green-900/20 dark:border-green-600'
                            : 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-600'
                        }`}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`flex-shrink-0 w-7 h-7 rounded-full flex items-center justify-center font-bold text-sm ${
                            isCorrect && isUserAnswer
                              ? 'bg-green-600 text-white'
                              : 'bg-gray-300 text-gray-700 dark:bg-gray-600 dark:text-gray-300'
                          }`}>
                            {choice}
                          </div>
                          <div className="flex-1">
                            <div className={`font-medium ${
                              isCorrect && isUserAnswer
                                ? 'text-green-900 dark:text-green-100'
                                : 'text-gray-900 dark:text-gray-100'
                            }`}>
                              {choiceText}
                            </div>
                            {isCorrect && isUserAnswer && (
                              <div className="flex items-center gap-1 mt-1">
                                <CheckCircle className="h-4 w-4 text-green-600" />
                                <span className="text-sm text-green-700 dark:text-green-300 font-medium">
                                  Your Correct Answer
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-900 dark:text-green-100">
                    You got the answer correct! (Understanding Score: {currentAnalysis.understandingScore}/10)
                  </span>
                </div>
                <p className="text-sm text-green-800 dark:text-green-200">
                  {currentAnalysis.feedback}
                </p>
              </div>

              {/* Detailed Correct Answer Explanation */}
              {currentAnalysis.correctAnswerExplanation && (
                <div className="space-y-4 mb-6">
                  {/* Why the answer is correct */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Why Option {currentQuestion.originalQuestion.correctAnswer} is Correct:
                    </h4>
                    <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
                      {currentAnalysis.correctAnswerExplanation.whyCorrect}
                    </p>

                    {/* Key Principles */}
                    {currentAnalysis.correctAnswerExplanation.keyPrinciples?.length > 0 && (
                      <div className="mb-3">
                        <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Key Principles:</h5>
                        <ul className="text-sm space-y-1">
                          {currentAnalysis.correctAnswerExplanation.keyPrinciples.map((principle: string, index: number) => (
                            <li key={index} className="flex items-start gap-2">
                              <CheckCircle className="h-3 w-3 mt-1 text-blue-600 flex-shrink-0" />
                              {principle}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Practical Application */}
                    {currentAnalysis.correctAnswerExplanation.practicalApplication && (
                      <div className="bg-blue-100 dark:bg-blue-800/30 rounded p-3">
                        <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Practical Application:</h5>
                        <p className="text-sm text-blue-800 dark:text-blue-200">
                          {currentAnalysis.correctAnswerExplanation.practicalApplication}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Why other options are wrong */}
                  {currentAnalysis.correctAnswerExplanation.whyOthersWrong && (
                    <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 rounded-lg p-4">
                      <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-3 flex items-center gap-2">
                        <XCircle className="h-4 w-4" />
                        Why Other Options are Incorrect:
                      </h4>
                      <div className="space-y-2">
                        {(['A', 'B', 'C', 'D'] as const).map((option) => {
                          if (option === currentQuestion.originalQuestion.correctAnswer) return null;
                          const explanation = currentAnalysis.correctAnswerExplanation?.whyOthersWrong[option];
                          if (!explanation) return null;

                          return (
                            <div key={option} className="flex items-start gap-2">
                              <span className="font-medium text-orange-700 dark:text-orange-300 min-w-[20px]">
                                {option})
                              </span>
                              <span className="text-sm text-orange-800 dark:text-orange-200">
                                {explanation}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {/* Related Topics */}
                  {currentAnalysis.correctAnswerExplanation.relatedTopics?.length > 0 && (
                    <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 rounded-lg p-4">
                      <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2 flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        Related GDPR Topics:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {currentAnalysis.correctAnswerExplanation.relatedTopics.map((topic: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs bg-purple-100 text-purple-800 border-purple-300">
                            {topic}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="space-y-4">
                <p className="text-gray-700 dark:text-gray-300 text-center">
                  Since you answered correctly, you have three options:
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-2 border-blue-200 hover:border-blue-400 cursor-pointer transition-colors">
                    <CardContent className="p-4">
                      <Button
                        onClick={handleChoiceToFollowUp}
                        className="w-full h-auto flex flex-col items-center gap-3 py-4"
                        variant="outline"
                      >
                        <Brain className="h-6 w-6 text-blue-600" />
                        <div className="text-center">
                          <div className="font-semibold">Deepen Understanding</div>
                          <div className="text-xs text-gray-600 mt-1">
                            Answer a follow-up question to test deeper knowledge
                          </div>
                        </div>
                      </Button>
                    </CardContent>
                  </Card>

                  <Card className="border-2 border-yellow-200 hover:border-yellow-400 cursor-pointer transition-colors">
                    <CardContent className="p-4">
                      <Button
                        onClick={handleManualMastery}
                        className="w-full h-auto flex flex-col items-center gap-3 py-4"
                        variant="outline"
                      >
                        <Trophy className="h-6 w-6 text-yellow-600" />
                        <div className="text-center">
                          <div className="font-semibold">Mark as Mastered</div>
                          <div className="text-xs text-gray-600 mt-1">
                            I understand this well enough - remove from Repair Center
                          </div>
                        </div>
                      </Button>
                    </CardContent>
                  </Card>

                  <Card className="border-2 border-green-200 hover:border-green-400 cursor-pointer transition-colors">
                    <CardContent className="p-4">
                      <Button
                        onClick={handleChoiceToSkip}
                        className="w-full h-auto flex flex-col items-center gap-3 py-4"
                        variant="outline"
                      >
                        <ArrowRight className="h-6 w-6 text-green-600" />
                        <div className="text-center">
                          <div className="font-semibold">Next Question</div>
                          <div className="text-xs text-gray-600 mt-1">
                            Move to the next question (you can review this later)
                          </div>
                        </div>
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                {currentAnalysis.followUpQuestion && (
                  <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                      Preview of follow-up question:
                    </h4>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      &quot;{currentAnalysis.followUpQuestion.question}&quot;
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Question Review - Show Original Question with Correct Answer */}
        {currentPhase === 'completed' && currentAnalysis && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-purple-600" />
                Question Review
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Original Question */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  {currentQuestion.originalQuestion.question}
                </h3>

                {/* Answer Choices with Correct Answer Highlighted */}
                <div className="space-y-3">
                  {(['A', 'B', 'C', 'D'] as const).map((choice) => {
                    const isCorrect = choice === currentQuestion.originalQuestion.correctAnswer;
                    const isUserAnswer = choice === selectedAnswer;
                    const choiceText = currentQuestion.originalQuestion[`choice${choice}` as keyof typeof currentQuestion.originalQuestion] as string;

                    return (
                      <div
                        key={choice}
                        className={`p-4 rounded-lg border-2 transition-all ${
                          isCorrect
                            ? 'bg-green-50 border-green-300 dark:bg-green-900/20 dark:border-green-600'
                            : isUserAnswer && !isCorrect
                            ? 'bg-red-50 border-red-300 dark:bg-red-900/20 dark:border-red-600'
                            : 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-600'
                        }`}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                            isCorrect
                              ? 'bg-green-600 text-white'
                              : isUserAnswer && !isCorrect
                              ? 'bg-red-600 text-white'
                              : 'bg-gray-300 text-gray-700 dark:bg-gray-600 dark:text-gray-300'
                          }`}>
                            {choice}
                          </div>
                          <div className="flex-1">
                            <div className={`font-medium ${
                              isCorrect
                                ? 'text-green-900 dark:text-green-100'
                                : isUserAnswer && !isCorrect
                                ? 'text-red-900 dark:text-red-100'
                                : 'text-gray-900 dark:text-gray-100'
                            }`}>
                              {choiceText}
                            </div>
                            {isCorrect && (
                              <div className="flex items-center gap-1 mt-1">
                                <CheckCircle className="h-4 w-4 text-green-600" />
                                <span className="text-sm text-green-700 dark:text-green-300 font-medium">
                                  Correct Answer
                                </span>
                              </div>
                            )}
                            {isUserAnswer && !isCorrect && (
                              <div className="flex items-center gap-1 mt-1">
                                <XCircle className="h-4 w-4 text-red-600" />
                                <span className="text-sm text-red-700 dark:text-red-300 font-medium">
                                  Your Answer
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Answer Summary */}
                <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm">
                      <Target className="h-4 w-4 text-blue-600" />
                      <span className="text-blue-900 dark:text-blue-100">
                        <strong>Your Answer:</strong> {selectedAnswer} |
                        <strong> Correct Answer:</strong> {currentQuestion.originalQuestion.correctAnswer}
                      </span>
                    </div>
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                      currentAnalysis.isAnswerCorrect
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                    }`}>
                      {currentAnalysis.isAnswerCorrect ? '✓ Correct' : '✗ Incorrect'}
                    </div>
                  </div>
                  {!currentAnalysis.isAnswerCorrect && (
                    <div className="mt-2 text-xs text-blue-700 dark:text-blue-300">
                      💡 Study the correct answer above and the detailed explanation below
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* AI Analysis Results */}
        {currentAnalysis && currentPhase !== 'answer' && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-600" />
                AI Analysis - Understanding Score: {currentAnalysis.understandingScore || currentAnalysis.followUpAnalysis?.overallUnderstanding}/10
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Initial Analysis */}
              {currentAnalysis.feedback && (
                <div className={`p-3 rounded-lg ${
                  currentAnalysis.isAnswerCorrect
                    ? 'bg-green-50 border border-green-200 dark:bg-green-900/20'
                    : 'bg-red-50 border border-red-200 dark:bg-red-900/20'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {currentAnalysis.isAnswerCorrect ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="font-medium">
                      {currentAnalysis.isAnswerCorrect ? 'Correct Answer' : 'Incorrect Answer'}
                    </span>
                  </div>
                  <p className="text-sm">{currentAnalysis.feedback}</p>
                </div>
              )}

              {/* Knowledge Gaps */}
              {currentAnalysis.knowledgeGaps?.length > 0 && (
                <div>
                  <h4 className="font-semibold text-orange-700 dark:text-orange-400 mb-2">Knowledge Gaps:</h4>
                  <ul className="text-sm space-y-1">
                    {currentAnalysis.knowledgeGaps.map((gap: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <AlertTriangle className="h-3 w-3 mt-1 text-orange-600 flex-shrink-0" />
                        {gap}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Detailed Correct Answer Explanation for incorrect answers */}
              {!currentAnalysis.isAnswerCorrect && currentAnalysis.correctAnswerExplanation && (
                <div className="border-t pt-4 space-y-4">
                  <h4 className="font-semibold text-blue-700 dark:text-blue-400 mb-3">Understanding the Correct Answer:</h4>

                  {/* Why the correct answer is right */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg p-4">
                    <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Why Option {currentQuestion.originalQuestion.correctAnswer} is Correct:
                    </h5>
                    <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
                      {currentAnalysis.correctAnswerExplanation.whyCorrect}
                    </p>

                    {/* Key Principles */}
                    {currentAnalysis.correctAnswerExplanation.keyPrinciples?.length > 0 && (
                      <div>
                        <h6 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Key Principles:</h6>
                        <ul className="text-sm space-y-1">
                          {currentAnalysis.correctAnswerExplanation.keyPrinciples.map((principle: string, index: number) => (
                            <li key={index} className="flex items-start gap-2">
                              <Target className="h-3 w-3 mt-1 text-blue-600 flex-shrink-0" />
                              {principle}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Why your answer was wrong */}
                  {currentAnalysis.correctAnswerExplanation.whyOthersWrong && currentAnalysis.correctAnswerExplanation.whyOthersWrong[currentQuestion.userAnswer as 'A' | 'B' | 'C' | 'D'] && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 rounded-lg p-4">
                      <h5 className="font-medium text-red-900 dark:text-red-100 mb-2 flex items-center gap-2">
                        <XCircle className="h-4 w-4 text-red-600" />
                        Why Your Answer (Option {currentQuestion.userAnswer}) was Incorrect:
                      </h5>
                      <p className="text-sm text-red-800 dark:text-red-200">
                        {currentAnalysis.correctAnswerExplanation.whyOthersWrong[currentQuestion.userAnswer as 'A' | 'B' | 'C' | 'D']}
                      </p>
                    </div>
                  )}

                  {/* Related Topics */}
                  {currentAnalysis.correctAnswerExplanation.relatedTopics?.length > 0 && (
                    <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 rounded-lg p-3">
                      <h5 className="font-medium text-purple-900 dark:text-purple-100 mb-2 flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        Study These Related Topics:
                      </h5>
                      <div className="flex flex-wrap gap-2">
                        {currentAnalysis.correctAnswerExplanation.relatedTopics.map((topic: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs bg-purple-100 text-purple-800 border-purple-300">
                            {topic}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Follow-up Analysis */}
              {currentAnalysis.followUpAnalysis && (
                <div className="border-t pt-4">
                  <h4 className="font-semibold text-purple-700 dark:text-purple-400 mb-2">
                    Follow-up Analysis (Score: {currentAnalysis.followUpAnalysis.followUpScore}/10):
                  </h4>
                  <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
                    {currentAnalysis.followUpAnalysis.feedback}
                  </p>

                  {currentAnalysis.followUpAnalysis.conceptsMastered?.length > 0 && (
                    <div className="mb-3">
                      <h5 className="font-medium text-green-700 dark:text-green-400 mb-1">Concepts Mastered:</h5>
                      <ul className="text-sm space-y-1">
                        {currentAnalysis.followUpAnalysis.conceptsMastered.map((concept: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircle className="h-3 w-3 mt-1 text-green-600 flex-shrink-0" />
                            {concept}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {currentAnalysis.followUpAnalysis.areasForImprovement?.length > 0 && (
                    <div className="mb-3">
                      <h5 className="font-medium text-orange-700 dark:text-orange-400 mb-1">Areas for Improvement:</h5>
                      <ul className="text-sm space-y-1">
                        {currentAnalysis.followUpAnalysis.areasForImprovement.map((area: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <Target className="h-3 w-3 mt-1 text-orange-600 flex-shrink-0" />
                            {area}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {currentAnalysis.followUpAnalysis.studyRecommendations?.length > 0 && (
                    <div>
                      <h5 className="font-medium text-blue-700 dark:text-blue-400 mb-1">Study Recommendations:</h5>
                      <ul className="text-sm space-y-1">
                        {currentAnalysis.followUpAnalysis.studyRecommendations.map((rec: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <BookOpen className="h-3 w-3 mt-1 text-blue-600 flex-shrink-0" />
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Mark as Mastered Section for Correct Answers */}
        {currentPhase === 'completed' && currentAnalysis && currentAnalysis.isAnswerCorrect && currentAnalysis.understandingScore < 7 && (
          <Card className="mb-6 bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="mb-4">
                  <div className="w-16 h-16 mx-auto bg-yellow-100 dark:bg-yellow-800/30 rounded-full flex items-center justify-center mb-3">
                    <CheckCircle className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                    You Got It Correct!
                  </h3>
                  <p className="text-sm text-yellow-800 dark:text-yellow-200 mb-4">
                    You answered correctly (Score: {currentAnalysis.understandingScore}/10). Do you feel confident enough to remove this from your Repair Center?
                  </p>
                </div>

                <div className="bg-yellow-100 dark:bg-yellow-800/30 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">Your Options:</h4>
                  <ul className="text-sm text-yellow-800 dark:text-yellow-200 space-y-1 mb-4">
                    <li>• <strong>Mark as Mastered:</strong> Remove from Repair Center (you understand it well)</li>
                    <li>• <strong>Keep Practicing:</strong> Leave in Repair Center for future review</li>
                  </ul>

                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button
                      onClick={handleManualMastery}
                      className="bg-yellow-600 hover:bg-yellow-700 text-white"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Mark as Mastered
                    </Button>
                    <Button
                      variant="outline"
                      className="border-yellow-300 text-yellow-700 hover:bg-yellow-50"
                      onClick={() => {
                        toast({
                          title: "Staying in Repair Center",
                          description: "This question will remain available for future practice.",
                        });
                      }}
                    >
                      <BookOpen className="h-4 w-4 mr-2" />
                      Keep for Practice
                    </Button>
                  </div>
                </div>

                <div className="text-xs text-yellow-700 dark:text-yellow-300">
                  💡 You can always practice this question again later if you mark it as mastered
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Continue Learning Section for Incorrect Answers */}
        {currentPhase === 'completed' && currentAnalysis && !currentAnalysis.isAnswerCorrect && (
          <Card className="mb-6 bg-blue-50 dark:bg-blue-900/20 border-blue-200">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="mb-4">
                  <div className="w-16 h-16 mx-auto bg-blue-100 dark:bg-blue-800/30 rounded-full flex items-center justify-center mb-3">
                    <BookOpen className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    Keep Learning!
                  </h3>
                  <p className="text-sm text-blue-800 dark:text-blue-200 mb-4">
                    You&apos;ve reviewed the correct answer and explanation. This question will remain in your Repair Center for more practice.
                  </p>
                </div>

                <div className="bg-blue-100 dark:bg-blue-800/30 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">What happens next?</h4>
                  <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <li>• This question stays in your Repair Center for future practice</li>
                    <li>• Review the explanation above to understand the concept</li>
                    <li>• Continue to the next question to keep learning</li>
                    <li>• You can return to practice this question again later</li>
                  </ul>
                </div>

                <div className="flex items-center justify-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                  <Target className="h-4 w-4" />
                  <span>Ready to continue? Use the navigation buttons below.</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous Question
          </Button>

          {currentQuestionIndex < topicQuestions.length - 1 ? (
            <Button
              onClick={handleNextQuestion}
              disabled={currentPhase === 'answer' || currentPhase === 'follow_up'}
              className={currentPhase === 'completed' ? 'bg-blue-600 hover:bg-blue-700' : ''}
            >
              {currentPhase === 'completed' && currentAnalysis && !currentAnalysis.isAnswerCorrect
                ? 'Continue Learning'
                : 'Next Question'
              }
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={onBack}
              disabled={currentPhase === 'answer' || currentPhase === 'follow_up'}
              className="bg-green-600 hover:bg-green-700"
            >
              Complete Session
              <CheckCircle className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>

        {/* Session Summary */}
        {currentQuestionIndex === topicQuestions.length - 1 && currentPhase === 'completed' && (
          <Card className="mt-6 bg-green-50 dark:bg-green-900/20 border-green-200">
            <CardContent className="p-6 text-center">
              <h3 className="text-lg font-bold text-green-900 dark:text-green-100 mb-2">
                Deep Understanding Session Complete! 🎉
              </h3>
              <div className="text-sm text-green-700 dark:text-green-300">
                <p>Questions Completed: {completedQuestions} / {topicQuestions.length}</p>
                <p>Session Time: {Math.floor((Date.now() - startTime) / 60000)} minutes</p>
                <p className="mt-2 text-xs">All progress and AI feedback has been saved for review.</p>
              </div>
              <div className="mt-4">
                <Button onClick={onBack}>
                  Back to Repair Center
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Mastery Celebration Animation */}
        <AnimatePresence>
          {showMasteryAnimation && masteredQuestionData && (
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.5 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
            >
              <motion.div
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -50, opacity: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white dark:bg-gray-800 rounded-2xl p-8 mx-4 max-w-md w-full text-center shadow-2xl border-4 border-green-400"
              >
                {/* Trophy Icon with Animation */}
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.8, delay: 0.3, type: "spring", bounce: 0.6 }}
                  className="mb-6"
                >
                  <div className="w-20 h-20 mx-auto bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center shadow-lg">
                    <motion.div
                      animate={{ rotate: [0, -10, 10, -10, 0] }}
                      transition={{ duration: 0.6, delay: 1, repeat: 2 }}
                    >
                      🏆
                    </motion.div>
                  </div>
                </motion.div>

                {/* Title */}
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2"
                >
                  Question Mastered!
                </motion.h2>

                {/* Score */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                  className="mb-4"
                >
                  <div className="text-6xl font-bold text-green-500 mb-2">
                    {masteredQuestionData.score}/10
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">
                    Excellent Understanding!
                  </div>
                </motion.div>

                {/* Progress */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.9 }}
                  className="mb-6"
                >
                  <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    Question {masteredQuestionData.questionNumber} of {masteredQuestionData.totalQuestions}
                  </div>
                  <div className="bg-green-100 dark:bg-green-900/30 rounded-full p-3">
                    <div className="text-sm text-green-800 dark:text-green-200 font-medium">
                      🎯 This question will be removed from your Repair Center
                    </div>
                  </div>
                </motion.div>

                {/* Celebration Text */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 1.1 }}
                  className="text-gray-600 dark:text-gray-400 text-sm"
                >
                  Keep up the great work! 🌟
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Confetti Component */}
        <Confetti onInit={({ confetti }) => {
          confettiRef.current = confetti;
        }} />
      </motion.div>
    </div>
  );
}
